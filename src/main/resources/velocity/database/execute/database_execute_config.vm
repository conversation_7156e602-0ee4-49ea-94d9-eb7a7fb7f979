#set($page_title='页面标题')

#parse("database/execute/database_execute_result.vm")

<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="任务名称">
            <el-input v-model="queryForm.name" placeholder="请输入任务名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
            <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
                <el-option label="待启动" value="SAVED"></el-option>
                <el-option label="待执行" value="WAIT"></el-option>
                <el-option label="执行中" value="RUNNING"></el-option>
                <el-option label="失败" value="FAILED"></el-option>
                <el-option label="成功" value="SUCCESS"></el-option>
                <el-option label="人工停止" value="STOPPED"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="数据库">
            <el-select v-model="queryForm.databaseId" placeholder="请选择数据库" clearable>
                <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId"></el-option>
            </el-select>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column label="" type="expand">
            <template slot-scope="props">
                <el-form label-position="left" inline class="table-expand">
                    <el-form-item label="创建时间">
                        <span>{{ props.row.createTime }}</span>
                    </el-form-item>
                    <el-form-item label="更新时间">
                        <span>{{ props.row.updateTime }}</span>
                    </el-form-item>
                    <el-form-item label="创建用户ID">
                        <span>{{ props.row.createUserId }}</span>
                    </el-form-item>
                    <el-form-item label="更新用户ID">
                        <span>{{ props.row.updateUserId }}</span>
                    </el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column prop="id" label="ID"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="databaseId" label="数据库ID"></el-table-column>
        <el-table-column prop="databaseName" label="数据库名称"></el-table-column>
        <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">{{ getStatusName(scope.row.status) }}</el-tag>
            </template>
        </el-table-column>
        <el-table-column prop="sql" label="SQL语句"></el-table-column>
        <el-table-column prop="keyStart" label="起始键值"></el-table-column>
        <el-table-column prop="keyEnd" label="结束键值"></el-table-column>
        <el-table-column prop="batchOffset" label="批次偏移量"></el-table-column>
        <el-table-column prop="currentKeyValue" label="当前键值"></el-table-column>
        <el-table-column prop="lastExecuteTime" label="最后执行时间"></el-table-column>
        <el-table-column label="进度" width="120">
            <template slot-scope="scope">
                <div v-if="scope.row.status === 'RUNNING' && scope.row.progressPercent !== undefined">
                    <el-progress :percentage="scope.row.progressPercent" :stroke-width="6"></el-progress>
                </div>
                <span v-else>-</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                <el-button type="success" size="small" @click="handleExecute(scope.row)"
                           v-if="canExecute(scope.row.status)" :loading="scope.row.executing">执行</el-button>
                <el-button type="warning" size="small" @click="handleStop(scope.row)"
                           v-if="canStop(scope.row.status)" :loading="scope.row.stopping">停止</el-button>
                <el-button type="info" size="small" @click="refreshProgress(scope.row)"
                           v-if="scope.row.status === 'RUNNING'">刷新</el-button>
                <el-button type="plain" size="small" @click="showResultDialog(scope.row.id)">结果</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="执行任务名称" maxlength="100" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="数据库" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择数据库" style="width: 100%">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="数据库名称" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="数据库名,允许为空" maxlength="100"></el-input>
            </el-form-item>
            <el-form-item label="SQL语句" prop="sql">
                <el-input type="textarea" :rows="6" v-model="addEditForm.sql"
                          placeholder="执行的SQL，该sql中应该包含:keyStart和:keyEnd两个变量占位符，确定每一批次执行的范围，左右闭区间。例如update t_table set val=id where id>=:keyStart and id<=:keyEnd"
                          maxlength="2000" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="起始键值" prop="keyStart">
                <el-input v-model="addEditForm.keyStart" placeholder="开始的主键，闭区间；目前仅支持数字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="结束键值" prop="keyEnd">
                <el-input v-model="addEditForm.keyEnd" placeholder="结束的主键，闭区间；目前仅支持数字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="批次偏移量" prop="batchOffset">
                <el-input v-model="addEditForm.batchOffset" placeholder="每次执行的偏移量，即每次执行范围=[当前key,当前key+batch_offset)" maxlength="20"></el-input>
            </el-form-item>
            <div style="padding-left: 80px; color: #909399; font-size: 12px;">
                <p>• 当起始值、结束值、偏移量没有填写时，只执行1次。</p>
                <p>• 批量执行时，SQL必须包含:keyStart和:keyEnd占位符。</p>
                <p>• 主键值目前仅支持数字类型。</p>
            </div>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="执行结果" :visible.sync="showResult" width="90%" top="10px" :close-on-click-modal="false">
        <database-execute-result v-if="showResult" :task-id="currentTaskId" />
        <span slot="footer">
            <el-button @click="showResult = false">关闭</el-button>
        </span>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10, name: '', status: '', databaseId: null}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {
                name: [
                    { required: true, message: '请输入任务名称', trigger: 'blur' },
                    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
                ],
                databaseId: [
                    { required: true, message: '请选择数据库', trigger: 'change' }
                ],
                sql: [
                    { required: true, message: '请输入SQL语句', trigger: 'blur' },
                    { min: 1, max: 2000, message: '长度在 1 到 2000 个字符', trigger: 'blur' }
                ],
                keyStart: [
                    { pattern: /^\d+$/, message: '起始键值必须是数字', trigger: 'blur' }
                ],
                keyEnd: [
                    { pattern: /^\d+$/, message: '结束键值必须是数字', trigger: 'blur' }
                ],
                batchOffset: [
                    { pattern: /^\d+$/, message: '批次偏移量必须是数字', trigger: 'blur' }
                ]
            },
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            databases: [],
            showResult: false, // 控制执行结果弹窗
            currentTaskId: null, // 当前查看的任务ID
            progressTimer: null // 进度刷新定时器
        },
        created: function() {
            this.getData()
            this.loadDatabases()
            this.startProgressTimer()
        },
        beforeDestroy: function() {
            this.stopProgressTimer()
        },
        methods: {
            loadDatabases: function() {
                var that = this
                Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
                    that.databases = resp.data
                })
            },
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_execute_config/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                    // 为运行中的任务加载进度
                    that.loadProgressForRunningTasks()
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
                this.getData()
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_execute_config/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增数据库执行任务配置表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_execute_config/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            showResultDialog: function(taskId) {
                this.currentTaskId = taskId
                this.showResult = true
            },
            getStatusName: function(status) {
                var statusMap = {
                    'SAVED': '待启动',
                    'WAIT': '待执行',
                    'RUNNING': '执行中',
                    'FAILED': '失败',
                    'SUCCESS': '成功',
                    'STOPPED': '人工停止'
                }
                return statusMap[status] || status
            },
            getStatusType: function(status) {
                var typeMap = {
                    'SAVED': 'primary',
                    'WAIT': 'warning',
                    'RUNNING': 'primary',
                    'FAILED': 'danger',
                    'SUCCESS': 'success',
                    'STOPPED': 'info'
                }
                return typeMap[status] || 'info'
            },
            canExecute: function(status) {
                return status === 'SAVED' || status === 'FAILED'
            },
            canStop: function(status) {
                return status === 'RUNNING' || status === 'WAIT'
            },
            handleExecute: function(row) {
                var that = this
                Message.confirm("确定要执行该任务吗？", function(){
                    that.$set(row, 'executing', true)
                    Resource.post("${_contextPath_}/database_execute_config/start_execute", {id: row.id}, function(){
                        Message.success("任务已开始执行")
                        that.$set(row, 'executing', false)
                        that.getData()
                    }, function() {
                        that.$set(row, 'executing', false)
                    })
                })
            },
            handleStop: function(row) {
                var that = this
                Message.confirm("确定要停止该任务吗？", function(){
                    that.$set(row, 'stopping', true)
                    Resource.post("${_contextPath_}/database_execute_config/stop_execute", {id: row.id}, function(){
                        Message.success("任务已停止")
                        that.$set(row, 'stopping', false)
                        that.getData()
                    }, function() {
                        that.$set(row, 'stopping', false)
                    })
                })
            },
            refreshProgress: function(row) {
                var that = this
                Resource.get("${_contextPath_}/database_execute_config/get_progress", {id: row.id}, function(resp){
                    if (resp.data && resp.data.progressPercent !== undefined) {
                        that.$set(row, 'progressPercent', resp.data.progressPercent)
                    }
                })
            },
            loadProgressForRunningTasks: function() {
                var that = this
                this.tableData.forEach(function(row) {
                    if (row.status === 'RUNNING') {
                        that.refreshProgress(row)
                    }
                })
            },
            startProgressTimer: function() {
                var that = this
                this.progressTimer = setInterval(function() {
                    that.loadProgressForRunningTasks()
                }, 5000) // 每5秒刷新一次进度
            },
            stopProgressTimer: function() {
                if (this.progressTimer) {
                    clearInterval(this.progressTimer)
                    this.progressTimer = null
                }
            }
        }
    })
</script>
