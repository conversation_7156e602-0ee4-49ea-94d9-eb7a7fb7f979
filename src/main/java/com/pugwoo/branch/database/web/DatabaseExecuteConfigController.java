package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.database.entity.DatabaseExecuteConfigDO;
import com.pugwoo.branch.database.service.DatabaseExecuteConfigService;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

@RestController
@RequestMapping(value = "/database_execute_config")
public class DatabaseExecuteConfigController {

    @Autowired
    private DatabaseExecuteConfigService databaseExecuteConfigService;
    
    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("database/execute/database_execute_config");
    }

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize, String name, String status, Long databaseId) {
        PageData<DatabaseExecuteConfigDO> pageData = databaseExecuteConfigService.getPage(page, pageSize, name, status, databaseId);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<?> addOrUpdate(DatabaseExecuteConfigDO databaseExecuteConfigDO) {
        WebCheckUtils.assertNotNull(databaseExecuteConfigDO, "缺少修改的对象参数");

        // Parameter validation
        WebCheckUtils.assertNotBlank(databaseExecuteConfigDO.getName(), "任务名称不能为空");
        WebCheckUtils.assertNotNull(databaseExecuteConfigDO.getDatabaseId(), "数据库ID不能为空");
        WebCheckUtils.assertNotBlank(databaseExecuteConfigDO.getSql(), "SQL语句不能为空");

        // Validate SQL contains required placeholders if batch execution is configured
        if (databaseExecuteConfigDO.getKeyStart() != null && databaseExecuteConfigDO.getKeyEnd() != null) {
            String sql = databaseExecuteConfigDO.getSql();
            if (sql != null && (!sql.contains(":keyStart") || !sql.contains(":keyEnd"))) {
                return WebJsonBean.fail("批量执行的SQL必须包含:keyStart和:keyEnd占位符");
            }
        }

        ResultBean<Long> result = databaseExecuteConfigService.insertOrUpdate(databaseExecuteConfigDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(databaseExecuteConfigService.deleteById(id));
    }

    @PostMapping("start_execute")
    public WebJsonBean<?> startExecute(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        try {
            databaseExecuteConfigService.startExecute(id);
            return WebJsonBean.ok(true);
        } catch (Exception e) {
            return WebJsonBean.fail("启动执行失败: " + e.getMessage());
        }
    }

    @PostMapping("stop_execute")
    public WebJsonBean<?> stopExecute(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        try {
            databaseExecuteConfigService.stopExecute(id);
            return WebJsonBean.ok(true);
        } catch (Exception e) {
            return WebJsonBean.fail("停止执行失败: " + e.getMessage());
        }
    }

    @GetMapping("get_progress")
    public WebJsonBean<Map<String, Object>> getProgress(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        Map<String, Object> progress = databaseExecuteConfigService.getExecutionProgress(id);
        return WebJsonBean.ok(progress);
    }

}
